# NixOS test system configuration
# This is the host-specific configuration for the nixos-test system
{ config, pkgs, lib, username, hostname, ... }:

{
  # Import hardware configuration (will be generated by nixos-generate-config)
  imports = [
    # ./hardware-configuration.nix  # This will be generated on the target system
  ];

  # Boot configuration for VM
  boot = {
    # Use GRUB for VM compatibility
    loader = {
      grub = {
        enable = true;
        device = "/dev/sda"; # Adjust based on VM disk setup
        # For UEFI VMs, use:
        # efiSupport = true;
        # device = "nodev";
      };
      # systemd-boot.enable = false; # Disable systemd-boot for GRUB
    };

    # Enable VM guest additions
    initrd.availableKernelModules = [ "ata_piix" "ohci_pci" "ehci_pci" "ahci" "sd_mod" "sr_mod" ];
    kernelModules = [ ];
  };

  # File systems - basic configuration for VM
  fileSystems."/" = {
    device = "/dev/disk/by-label/nixos";
    fsType = "ext4";
  };

  # Swap configuration
  swapDevices = [ ];

  # Networking configuration
  networking = {
    hostName = hostname;
    # Enable DHCP for VM
    useDHCP = lib.mkDefault true;
    # interfaces.enp0s3.useDHCP = lib.mkDefault true; # Adjust interface name as needed
  };

  # Hardware configuration
  hardware = {
    # Enable all firmware
    enableAllFirmware = true;
    # CPU microcode updates
    cpu.intel.updateMicrocode = lib.mkDefault config.hardware.enableRedistributableFirmware;
    # cpu.amd.updateMicrocode = lib.mkDefault config.hardware.enableRedistributableFirmware;
  };

  # System packages specific to this host
  environment.systemPackages = with pkgs; [
    # VM tools
    open-vm-tools
    # System info tools
    fastfetch
    neofetch
    # Basic utilities
    vim
    git
    curl
    wget
  ];

  # SSH service configuration for testing - minimal config to avoid conflicts
  services.openssh = {
    enable = true;
    settings = {
      PasswordAuthentication = true; # Allow password auth for VM testing
      PermitRootLogin = "yes";
    };
  };

  # VM guest tools
  virtualisation.vmware.guest.enable = true;

  # User configuration - set password for testing
  users.users.${username} = {
    # Set a default password for testing (change this!)
    password = "nixos";
    # Enable sudo without password for convenience in testing
    extraGroups = [ "wheel" ];
  };

  users.users.root = {
    password = "nixos";
  };

  # Enable sudo without password for wheel group (testing only!)
  security.sudo.wheelNeedsPassword = false;

  # System state version
  system.stateVersion = "24.05";
}
