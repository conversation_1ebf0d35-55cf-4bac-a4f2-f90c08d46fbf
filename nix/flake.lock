{"nodes": {"brew-src": {"flake": false, "locked": {"lastModified": 1749511373, "narHash": "sha256-7u1TdHQaUCzzgf/n8T3bQosuYXyNBEPU/3WQQqozE5o=", "owner": "Homebrew", "repo": "brew", "rev": "7b4ef99fed96966269ee35994407fa4c06097a4d", "type": "github"}, "original": {"owner": "Homebrew", "ref": "4.5.6", "repo": "brew", "type": "github"}}, "darwin": {"inputs": {"nixpkgs": ["nixpkgs-darwin"]}, "locked": {"lastModified": 1751313918, "narHash": "sha256-HsJM3XLa43WpG+665aGEh8iS8AfEwOIQWk3Mke3e7nk=", "owner": "lnl7", "repo": "nix-darwin", "rev": "e04a388232d9a6ba56967ce5b53a8a6f713cdfcf", "type": "github"}, "original": {"owner": "lnl7", "ref": "master", "repo": "nix-darwin", "type": "github"}}, "flake-parts": {"inputs": {"nixpkgs-lib": ["nixvim", "nixpkgs"]}, "locked": {"lastModified": 1751413152, "narHash": "sha256-Tyw1RjYEsp5scoigs1384gIg6e0GoBVjms4aXFfRssQ=", "owner": "hercules-ci", "repo": "flake-parts", "rev": "77826244401ea9de6e3bac47c2db46005e1f30b5", "type": "github"}, "original": {"owner": "hercules-ci", "repo": "flake-parts", "type": "github"}}, "flake-utils": {"inputs": {"systems": "systems"}, "locked": {"lastModified": 1731533236, "narHash": "sha256-l0KFg5HjrsfsO/JpG+r7fRrqm12kzFHyUHqHCVpMMbI=", "owner": "numtide", "repo": "flake-utils", "rev": "11707dc2f618dd54ca8739b309ec4fc024de578b", "type": "github"}, "original": {"id": "flake-utils", "type": "indirect"}}, "flake-utils_2": {"inputs": {"systems": "systems_2"}, "locked": {"lastModified": 1726560853, "narHash": "sha256-X6rJYSESBVr3hBoH0WbKE5KvhPU5bloyZ2L4K60/fPQ=", "owner": "numtide", "repo": "flake-utils", "rev": "c1dfcf08411b08f6b8615f7d8971a2bfa81d5e8a", "type": "github"}, "original": {"owner": "numtide", "repo": "flake-utils", "type": "github"}}, "flake-utils_3": {"inputs": {"systems": "systems_4"}, "locked": {"lastModified": 1731533236, "narHash": "sha256-l0KFg5HjrsfsO/JpG+r7fRrqm12kzFHyUHqHCVpMMbI=", "owner": "numtide", "repo": "flake-utils", "rev": "11707dc2f618dd54ca8739b309ec4fc024de578b", "type": "github"}, "original": {"owner": "numtide", "repo": "flake-utils", "type": "github"}}, "haumea": {"inputs": {"nixpkgs": ["nixhelm", "nixpkgs"]}, "locked": {"lastModified": 1685133229, "narHash": "sha256-FePm/Gi9PBSNwiDFq3N+DWdfxFq0UKsVVTJS3cQPn94=", "owner": "nix-community", "repo": "haumea", "rev": "34dd58385092a23018748b50f9b23de6266dffc2", "type": "github"}, "original": {"owner": "nix-community", "ref": "v0.2.2", "repo": "haumea", "type": "github"}}, "home-manager": {"inputs": {"nixpkgs": ["nixpkgs-darwin"]}, "locked": {"lastModified": 1752202894, "narHash": "sha256-knafgng4gCjZIUMyAEWjxxdols6n/swkYnbWr+oF+1w=", "owner": "nix-community", "repo": "home-manager", "rev": "fab659b346c0d4252208434c3c4b3983a4b38fec", "type": "github"}, "original": {"owner": "nix-community", "ref": "master", "repo": "home-manager", "type": "github"}}, "ixx": {"inputs": {"flake-utils": ["nixvim", "nuschtosSearch", "flake-utils"], "nixpkgs": ["nixvim", "nuschtosSearch", "nixpkgs"]}, "locked": {"lastModified": 1748294338, "narHash": "sha256-FVO01jdmUNArzBS7NmaktLdGA5qA3lUMJ4B7a05Iynw=", "owner": "NuschtOS", "repo": "ixx", "rev": "cc5f390f7caf265461d4aab37e98d2292ebbdb85", "type": "github"}, "original": {"owner": "NuschtOS", "ref": "v0.0.8", "repo": "ixx", "type": "github"}}, "nix-github-actions": {"inputs": {"nixpkgs": ["nixhelm", "poetry2nix", "nixpkgs"]}, "locked": {"lastModified": 1729742964, "narHash": "sha256-B4mzTcQ0FZHdpeWcpDYPERtyjJd/NIuaQ9+BV1h+MpA=", "owner": "nix-community", "repo": "nix-github-actions", "rev": "e04df33f62cdcf93d73e9a04142464753a16db67", "type": "github"}, "original": {"owner": "nix-community", "repo": "nix-github-actions", "type": "github"}}, "nix-homebrew": {"inputs": {"brew-src": "brew-src"}, "locked": {"lastModified": 1749952250, "narHash": "sha256-V2ix0knpdJXirQ+4pjbnggjdSALTsFWGIP/NDpaQkdU=", "owner": "zhaofengli-wip", "repo": "nix-homebrew", "rev": "37126f06f4890f019af3d7606ce5d30a457afcd0", "type": "github"}, "original": {"owner": "zhaofengli-wip", "repo": "nix-homebrew", "type": "github"}}, "nix-kube-generators": {"locked": {"lastModified": 1729269463, "narHash": "sha256-8jDDpC99fYl5CSHjZyPwb5PK7nQSknhkpfe8+DXI910=", "owner": "farcaller", "repo": "nix-kube-generators", "rev": "2be4f3cb99e179d9f94e6c8723862421437f8efb", "type": "github"}, "original": {"owner": "farcaller", "repo": "nix-kube-generators", "type": "github"}}, "nixhelm": {"inputs": {"flake-utils": "flake-utils", "haumea": "haumea", "nix-kube-generators": "nix-kube-generators", "nixpkgs": ["nixpkgs-darwin"], "poetry2nix": "poetry2nix"}, "locked": {"lastModified": 1753840310, "narHash": "sha256-tDLd6I14FUVIF/Oq9+P/izLq5XtcVvPUMx3iVWl7efY=", "owner": "nix-community", "repo": "nixhelm", "rev": "20a7515db13a24a5643fb72225e6acb7d952eb15", "type": "github"}, "original": {"owner": "nix-community", "repo": "nixhelm", "type": "github"}}, "nixpkgs-darwin": {"locked": {"lastModified": 1751949589, "narHash": "sha256-mgFxAPLWw0Kq+C8P3dRrZrOYEQXOtKuYVlo9xvPntt8=", "owner": "nixos", "repo": "nixpkgs", "rev": "9b008d60392981ad674e04016d25619281550a9d", "type": "github"}, "original": {"owner": "nixos", "ref": "nixpkgs-unstable", "repo": "nixpkgs", "type": "github"}}, "nixvim": {"inputs": {"flake-parts": "flake-parts", "nixpkgs": ["nixpkgs-darwin"], "nuschtosSearch": "nuschtosSearch", "systems": "systems_5"}, "locked": {"lastModified": 1752262661, "narHash": "sha256-jPDiaHsKZeFH+zoxRIW0t1T/R+S8cYM3/9YUfMMjUEA=", "owner": "nix-community", "repo": "nixvim", "rev": "658980fb247e2f10fa692bae372ac21389a67c6c", "type": "github"}, "original": {"owner": "nix-community", "repo": "nixvim", "type": "github"}}, "nuschtosSearch": {"inputs": {"flake-utils": "flake-utils_3", "ixx": "ixx", "nixpkgs": ["nixvim", "nixpkgs"]}, "locked": {"lastModified": 1749730855, "narHash": "sha256-L3x2nSlFkXkM6tQPLJP3oCBMIsRifhIDPMQQdHO5xWo=", "owner": "NuschtOS", "repo": "search", "rev": "8dfe5879dd009ff4742b668d9c699bc4b9761742", "type": "github"}, "original": {"owner": "NuschtOS", "repo": "search", "type": "github"}}, "poetry2nix": {"inputs": {"flake-utils": "flake-utils_2", "nix-github-actions": "nix-github-actions", "nixpkgs": ["nixhelm", "nixpkgs"], "systems": "systems_3", "treefmt-nix": "treefmt-nix"}, "locked": {"lastModified": 1738741221, "narHash": "sha256-UiTOA89yQV5YNlO1ZAp4IqJUGWOnTyBC83netvt8rQE=", "owner": "nix-community", "repo": "poetry2nix", "rev": "be1fe795035d3d36359ca9135b26dcc5321b31fb", "type": "github"}, "original": {"owner": "nix-community", "repo": "poetry2nix", "type": "github"}}, "root": {"inputs": {"darwin": "darwin", "home-manager": "home-manager", "nix-homebrew": "nix-homebrew", "nixhelm": "nixhelm", "nixpkgs-darwin": "nixpkgs-darwin", "nixvim": "nixvim"}}, "systems": {"locked": {"lastModified": 1681028828, "narHash": "sha256-Vy1rq5AaRuLzOxct8nz4T6wlgyUR7zLU309k9mBC768=", "owner": "nix-systems", "repo": "default", "rev": "da67096a3b9bf56a91d16901293e51ba5b49a27e", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default", "type": "github"}}, "systems_2": {"locked": {"lastModified": 1681028828, "narHash": "sha256-Vy1rq5AaRuLzOxct8nz4T6wlgyUR7zLU309k9mBC768=", "owner": "nix-systems", "repo": "default", "rev": "da67096a3b9bf56a91d16901293e51ba5b49a27e", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default", "type": "github"}}, "systems_3": {"locked": {"lastModified": 1681028828, "narHash": "sha256-Vy1rq5AaRuLzOxct8nz4T6wlgyUR7zLU309k9mBC768=", "owner": "nix-systems", "repo": "default", "rev": "da67096a3b9bf56a91d16901293e51ba5b49a27e", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default", "type": "github"}}, "systems_4": {"locked": {"lastModified": 1681028828, "narHash": "sha256-Vy1rq5AaRuLzOxct8nz4T6wlgyUR7zLU309k9mBC768=", "owner": "nix-systems", "repo": "default", "rev": "da67096a3b9bf56a91d16901293e51ba5b49a27e", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default", "type": "github"}}, "systems_5": {"locked": {"lastModified": 1681028828, "narHash": "sha256-Vy1rq5AaRuLzOxct8nz4T6wlgyUR7zLU309k9mBC768=", "owner": "nix-systems", "repo": "default", "rev": "da67096a3b9bf56a91d16901293e51ba5b49a27e", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default", "type": "github"}}, "treefmt-nix": {"inputs": {"nixpkgs": ["nixhelm", "poetry2nix", "nixpkgs"]}, "locked": {"lastModified": 1730120726, "narHash": "sha256-LqHYIxMrl/1p3/kvm2ir925tZ8DkI0KA10djk8wecSk=", "owner": "numtide", "repo": "treefmt-nix", "rev": "9ef337e492a5555d8e17a51c911ff1f02635be15", "type": "github"}, "original": {"owner": "numtide", "repo": "treefmt-nix", "type": "github"}}}, "root": "root", "version": 7}