{"id": null, "title": "<PERSON>er <PERSON>", "description": "Docker host metrics", "tags": ["system"], "style": "dark", "timezone": "browser", "editable": true, "hideControls": false, "sharedCrosshair": true, "rows": [{"collapse": false, "editable": true, "height": "100px", "panels": [{"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": "Prometheus", "decimals": 1, "editable": true, "error": false, "format": "s", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "id": 1, "interval": null, "isNew": true, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "s", "postfixFontSize": "80%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "span": 2, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "targets": [{"expr": "node_time_seconds - node_boot_time_seconds", "interval": "30s", "intervalFactor": 1, "refId": "A", "step": 30}], "thresholds": "", "title": "Uptime", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg", "timeFrom": "10s", "hideTimeOverride": true}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": "Prometheus", "editable": true, "error": false, "format": "percent", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "id": 13, "interval": null, "isNew": true, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "span": 2, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "targets": [{"expr": "sum(rate(node_cpu_seconds_total{mode=\"idle\"}[1m])) * 100 / scalar(count(node_cpu_seconds_total{mode=\"user\"}))", "interval": "10s", "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 20}], "thresholds": "", "title": "CPU Idle", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg", "timeFrom": "10s", "hideTimeOverride": true}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": "Prometheus", "editable": true, "error": false, "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "id": 12, "interval": null, "isNew": true, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "span": 2, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "targets": [{"expr": "machine_cpu_cores", "intervalFactor": 2, "metric": "machine_cpu_cores", "refId": "A", "step": 2}], "thresholds": "", "title": "CPU Cores", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg", "timeFrom": "10s", "hideTimeOverride": true}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": "Prometheus", "editable": true, "error": false, "format": "bytes", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "id": 2, "interval": null, "isNew": true, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "span": 2, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "targets": [{"expr": "node_memory_MemAvailable_bytes", "interval": "30s", "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 60}], "thresholds": "", "title": "Available Memory", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg", "timeFrom": "10s", "hideTimeOverride": true}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": "Prometheus", "editable": true, "error": false, "format": "bytes", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "id": 3, "interval": null, "isNew": true, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "span": 2, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "targets": [{"expr": "node_memory_SwapFree_bytes", "interval": "30s", "intervalFactor": 2, "refId": "A", "step": 60}], "thresholds": "", "title": "Free Swap", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg", "timeFrom": "10s", "hideTimeOverride": true}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": "Prometheus", "editable": true, "error": false, "format": "bytes", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "id": 4, "interval": null, "isNew": true, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "span": 2, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "targets": [{"expr": "sum(node_filesystem_free_bytes{fstype=\"ext4\"})", "interval": "30s", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 30}], "thresholds": "", "title": "Free Storage", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg", "timeFrom": "10s", "hideTimeOverride": true}], "title": "Available resources"}, {"collapse": false, "editable": true, "height": "150px", "panels": [{"aliasColors": {}, "bars": true, "datasource": "Prometheus", "decimals": 2, "editable": true, "error": false, "fill": 1, "grid": {"threshold1": null, "threshold1Color": "rgba(216, 200, 27, 0.27)", "threshold2": null, "threshold2Color": "rgba(234, 112, 112, 0.22)"}, "id": 9, "isNew": true, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": false, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "load 1m", "color": "#1F78C1"}], "span": 4, "stack": false, "steppedLine": false, "targets": [{"expr": "node_load1", "interval": "10s", "intervalFactor": 1, "legendFormat": "load 1m", "refId": "A", "step": 10}], "timeFrom": null, "timeShift": null, "title": "Load Average 1m", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"show": true}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}, {"aliasColors": {}, "bars": true, "datasource": "Prometheus", "editable": true, "error": false, "fill": 1, "grid": {"threshold1": null, "threshold1Color": "rgba(216, 200, 27, 0.27)", "threshold2": null, "threshold2Color": "rgba(234, 112, 112, 0.22)"}, "id": 10, "isNew": true, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": false, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "blocked by <PERSON><PERSON><PERSON>", "color": "#58140C"}], "span": 4, "stack": true, "steppedLine": false, "targets": [{"expr": "node_procs_running", "interval": "10s", "intervalFactor": 1, "legendFormat": "running", "metric": "node_procs_running", "refId": "A", "step": 10}, {"expr": "node_procs_blocked", "interval": "10s", "intervalFactor": 1, "legendFormat": "blocked by <PERSON><PERSON><PERSON>", "metric": "node_procs_blocked", "refId": "B", "step": 10}], "timeFrom": null, "timeShift": null, "title": "Processes", "tooltip": {"msResolution": true, "shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"show": true}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}, {"aliasColors": {}, "bars": true, "datasource": "Prometheus", "editable": true, "error": false, "fill": 1, "grid": {"threshold1": null, "threshold1Color": "rgba(216, 200, 27, 0.27)", "threshold2": null, "threshold2Color": "rgba(234, 112, 112, 0.22)"}, "id": 11, "isNew": true, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": false, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "interrupts", "color": "#806EB7"}], "span": 4, "stack": false, "steppedLine": false, "targets": [{"expr": " irate(node_intr_total[5m])", "interval": "10s", "intervalFactor": 1, "legendFormat": "interrupts", "metric": "node_intr_total", "refId": "A", "step": 10}], "timeFrom": null, "timeShift": null, "title": "Interrupts", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"show": true}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}], "title": "Load"}, {"collapse": false, "editable": true, "height": "250px", "panels": [{"aliasColors": {}, "bars": false, "datasource": "Prometheus", "decimals": 2, "editable": true, "error": false, "fill": 4, "grid": {"threshold1": null, "threshold1Color": "rgba(216, 200, 27, 0.27)", "threshold2": null, "threshold2Color": "rgba(234, 112, 112, 0.22)"}, "id": 5, "isNew": true, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 12, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(rate(node_cpu_seconds_total[1m])) by (mode) * 100 / scalar(count(node_cpu_seconds_total{mode=\"user\"}))", "intervalFactor": 10, "legendFormat": "{{ mode }}", "metric": "node_cpu_seconds_total", "refId": "A", "step": 10}], "timeFrom": null, "timeShift": null, "title": "CPU Usage", "tooltip": {"msResolution": true, "shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"show": true}, "yaxes": [{"format": "percent", "label": null, "logBase": 1, "max": 100, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}]}], "title": "CPU"}, {"collapse": false, "editable": true, "height": "250px", "panels": [{"aliasColors": {}, "bars": false, "datasource": "Prometheus", "decimals": 2, "editable": true, "error": false, "fill": 4, "grid": {"threshold1": null, "threshold1Color": "rgba(216, 200, 27, 0.27)", "threshold2": null, "threshold2Color": "rgba(234, 112, 112, 0.22)"}, "id": 6, "isNew": true, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "Used", "color": "#BF1B00"}, {"alias": "Free", "color": "#7EB26D"}, {"alias": "Buffers", "color": "#6ED0E0"}, {"alias": "<PERSON><PERSON><PERSON>", "color": "#EF843C"}], "span": 12, "stack": true, "steppedLine": false, "targets": [{"expr": "node_memory_MemTotal_bytes - (node_memory_MemFree_bytes + node_memory_Buffers_bytes + node_memory_Cached_bytes)", "intervalFactor": 1, "legendFormat": "Used", "refId": "A", "step": 1}, {"expr": "node_memory_MemFree_bytes", "intervalFactor": 1, "legendFormat": "Free", "refId": "B", "step": 1}, {"expr": "node_memory_Buffers_bytes", "intervalFactor": 1, "legendFormat": "Buffers", "refId": "C", "step": 1}, {"expr": "node_memory_Cached_bytes", "intervalFactor": 1, "legendFormat": "<PERSON><PERSON><PERSON>", "refId": "D", "step": 1}], "timeFrom": null, "timeShift": null, "title": "Memory Usage", "tooltip": {"msResolution": true, "shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"show": true}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}], "title": "Memory"}, {"collapse": false, "editable": true, "height": "250px", "panels": [{"aliasColors": {}, "bars": false, "datasource": "Prometheus", "decimals": 2, "editable": true, "error": false, "fill": 1, "grid": {"threshold1": null, "threshold1Color": "rgba(216, 200, 27, 0.27)", "threshold2": null, "threshold2Color": "rgba(234, 112, 112, 0.22)"}, "id": 7, "isNew": true, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "read", "yaxis": 1}, {"alias": "written", "yaxis": 1}, {"alias": "io time", "yaxis": 2}], "span": 12, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(irate(node_disk_read_bytes_total[1m]))", "interval": "", "intervalFactor": 1, "legendFormat": "read", "metric": "node_disk_read_bytes_total", "refId": "A", "step": 1}, {"expr": "sum(irate(node_disk_written_bytes_total[1m]))", "intervalFactor": 1, "legendFormat": "written", "metric": "node_disk_written_bytes_total", "refId": "B", "step": 1}, {"expr": "sum(irate(node_disk_io_time_seconds_total[1m]))", "intervalFactor": 1, "legendFormat": "io time", "metric": "node_disk_io_time_seconds_total", "refId": "C", "step": 1}], "timeFrom": null, "timeShift": null, "title": "I/O Usage", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"show": true}, "yaxes": [{"format": "Bps", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}], "title": "I/O"}, {"collapse": false, "editable": true, "height": "250px", "panels": [{"aliasColors": {}, "bars": false, "datasource": "Prometheus", "decimals": 2, "editable": true, "error": false, "fill": 4, "grid": {"threshold1": null, "threshold1Color": "rgba(216, 200, 27, 0.27)", "threshold2": null, "threshold2Color": "rgba(234, 112, 112, 0.22)"}, "id": 8, "isNew": true, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 12, "stack": true, "steppedLine": false, "targets": [{"expr": "irate(node_network_receive_bytes_total{device!=\"lo\"}[1m])", "intervalFactor": 1, "legendFormat": "In: {{ device }}", "metric": "node_network_receive_bytes_total", "refId": "A", "step": 1}, {"expr": "irate(node_network_transmit_bytes_total{device!=\"lo\"}[1m])", "intervalFactor": 1, "legendFormat": "Out: {{ device }}", "metric": "node_network_transmit_bytes_total", "refId": "B", "step": 1}], "timeFrom": null, "timeShift": null, "title": "Network Usage", "tooltip": {"msResolution": true, "shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"show": true}, "yaxes": [{"format": "Bps", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}], "title": "Network"}, {"collapse": false, "editable": true, "height": "250px", "panels": [{"aliasColors": {}, "bars": false, "datasource": "Prometheus", "decimals": 2, "editable": true, "error": false, "fill": 4, "grid": {"threshold1": null, "threshold1Color": "rgba(216, 200, 27, 0.27)", "threshold2": null, "threshold2Color": "rgba(234, 112, 112, 0.22)"}, "id": 14, "isNew": true, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "Used", "color": "#890F02"}, {"alias": "Free", "color": "#7EB26D"}], "span": 6, "stack": true, "steppedLine": false, "targets": [{"expr": "node_memory_SwapTotal_bytes - node_memory_SwapFree_bytes", "interval": "10s", "intervalFactor": 1, "legendFormat": "Used", "refId": "A", "step": 10}, {"expr": "node_memory_SwapFree_bytes", "interval": "10s", "intervalFactor": 1, "legendFormat": "Free", "refId": "B", "step": 10}], "timeFrom": null, "timeShift": null, "title": "S<PERSON>p <PERSON>", "tooltip": {"msResolution": true, "shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"show": true}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}, {"aliasColors": {}, "bars": false, "datasource": "Prometheus", "decimals": 2, "editable": true, "error": false, "fill": 1, "grid": {"threshold1": null, "threshold1Color": "rgba(216, 200, 27, 0.27)", "threshold2": null, "threshold2Color": "rgba(234, 112, 112, 0.22)"}, "id": 15, "isNew": true, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(node_vmstat_pswpin[1m]) * 4096 or irate(node_vmstat_pswpin[5m]) * 4096", "interval": "10s", "intervalFactor": 1, "legendFormat": "In", "refId": "A", "step": 10}, {"expr": "rate(node_vmstat_pswpout[1m]) * 4096 or irate(node_vmstat_pswpout[5m]) * 4096", "interval": "10s", "intervalFactor": 1, "legendFormat": "Out", "refId": "B", "step": 10}], "timeFrom": null, "timeShift": null, "title": "Swap I/O", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"show": true}, "yaxes": [{"format": "Bps", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}], "title": "New row"}], "time": {"from": "now-15m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "templating": {"list": []}, "annotations": {"list": []}, "refresh": "10s", "schemaVersion": 12, "version": 2, "links": [], "gnetId": null}