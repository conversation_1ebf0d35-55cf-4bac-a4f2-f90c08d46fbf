route:
    receiver: 'default'

receivers:
    - name: 'default'
      # 默认接收器 - 可以配置邮件、Slack、webhook等
      # 示例配置：
      # email_configs:
      #   - to: '<EMAIL>'
      #     from: '<EMAIL>'
      #     smarthost: 'localhost:587'
      #     subject: 'Alert: {{ .GroupLabels.alertname }}'
      #     body: '{{ .CommonAnnotations.description }}'

    # Slack 配置示例（需要配置实际的 webhook URL）
    # - name: 'slack'
    #   slack_configs:
    #       - send_resolved: true
    #         text: "{{ .CommonAnnotations.description }}"
    #         username: 'Prometheus'
    #         channel: '${SLACK_CHANNEL:-#alerts}'
    #         api_url: '${SLACK_WEBHOOK_URL}'
