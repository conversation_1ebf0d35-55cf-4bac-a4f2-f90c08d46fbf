{...}: {
  programs.ssh = {
    enable = true;

    # SSH client configuration
    extraConfig = ''
      # Disable strict host key checking (from ansible)
      StrictHostKeyChecking no
      UserKnownHostsFile /dev/null

      # Connection keep alive settings
      TCPKeepAlive yes
      ServerAliveInterval 15
      ServerAliveCountMax 6

      # Compression for slow connections
      Compression yes

      # Connection multiplexing
      ControlMaster auto
      ControlPath /tmp/%r@%h:%p
      ControlPersist yes

      # Additional useful settings
      # Automatically accept host keys for known hosts (less secure, but convenient)
      StrictHostKeyChecking accept-new

      # Hash known hosts for privacy
      HashKnownHosts yes

      # Enable verbose logging for debugging (uncomment if needed)
      # LogLevel VERBOSE

      # Forward SSH agent
      ForwardAgent yes

      # Enable X11 forwarding if needed
      ForwardX11 no

      # Disable GSSAPI authentication (speeds up connection)
      GSSAPIAuthentication no
      GSSAPIDelegateCredentials no

      # Preferred authentication methods
      PreferredAuthentications publickey,password

      # Ciphers and key exchange algorithms (security vs compatibility)
      Ciphers <EMAIL>,<EMAIL>,<EMAIL>,aes256-ctr,aes192-ctr,aes128-ctr
      MACs <EMAIL>,<EMAIL>,<EMAIL>,hmac-sha2-256,hmac-sha2-512
      HostKeyAlgorithms <EMAIL>,<EMAIL>,ssh-ed25519,ssh-rsa

      # Disable some deprecated or insecure features
      UseRoaming no
    '';
  };
}
