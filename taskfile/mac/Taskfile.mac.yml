---
version: '3'


# [Advanced macOS Commands - saurabhs.org](https://saurabhs.org/advanced-macos-commands)
#- launchctl list # 查看自启的services和process(通常 | grep来搜索 想要查找的计划任务)
#- pmset -g # 打印所有可用的电源配置信息
#- pmset -g | grep hibernatemode # hibernate(睡眠)和sleep(休眠)的区别在于内存是否供电，睡眠状态内存不供电不工作，内存数据落盘，休眠则内存继续工作；唤醒方式也不同，睡眠模式需要电源键启动，休眠模式通过“键鼠等输入设备”即可唤醒）。0，掉电非常严重；3，掉电一般严重；25，那掉电就会更少一些。
#- pmset repeat wakeorpoweron MTWRFSU 5:00:00 shutdown MTWRFSU 22:00:00 # pmset [repeat, schedule] [sleep, wake, poweron, shutdown, wakeorpoweron] [<MTWRFSU> <date/time>]
#- pmset -g sched # 查看crontab的电源计划
#- pmset displaysleepnow # 立即使显示器进入睡眠状态，而不使系统的其余部分进入睡眠状态
#- pmset sleepnow # 立即使整个系统进入睡眠状态
#- pmset -a disablesleep 1 # 作用为防止系统休眠；注意，输入这条指令后MacBook不会进入休眠状态
#- pmset -a disablesleep 0 # 恢复，上面命令的相反
#- caffeinate, # Running caffeinate with no flags or arguments prevents your Mac from going to sleep as long as the command continues to run.
#
#- pbcopy
#- pbpaste
#- sips
#- textutil
#- mdfind
#- mdls
#- screencapture
#- taskpolicy
#- say # announces the given message, just for English.
#- networksetup


tasks:
  # [fastfetch-cli/fastfetch: A maintained, feature-rich and performance oriented, neofetch like system information tool.](https://github.com/fastfetch-cli/fastfetch) 因为neofetch的性能差（因为基于bash实现）、并且已经EOL了，所以选择 fastfetch
  fetch:
    cmd: fastfetch


  df:
    cmd: df -lh # disk free

  du:
    cmd: du -sh * | sort -h # disk usage, 查看文件夹下各文件大小，并排序
    dir: '{{.USER_WORKING_DIR}}'






    #  - url: https://github.com/syncthing/syncthing
    #    des: 一个基于P2P实现的“远程同步文件”工具，提供GUI和CLI（通过web操作）两种下载方式，用homebrew安装，默认CLI。用这个就可以代替之前用的【坚果云】了 (Some time ago used Nutstore to sync code bidirectionally. I've also used other cloud service like icloud, dropbox, google-cloud to implement similar task.)。

    #- url: https://github.com/yeongpin/cursor-free-vip
    #  des: 目前可用的

  cursor:
    cmd: curl -fsSL https://aizaozao.com/accelerate.php/https://raw.githubusercontent.com/yuaotian/go-cursor-help/refs/heads/master/scripts/run/cursor_mac_id_modifier.sh -o ./cursor_mac_id_modifier.sh && sudo bash ./cursor_mac_id_modifier.sh && rm ./cursor_mac_id_modifier.sh
    dir: ~/Downloads

  # [nicolargo/glances](https://github.com/nicolargo/glances)
  top:
    cmd: glances # 更易用的top/htop，还支持web模式。但是其核心是“轻量级monitor系统”，glances还分可以分为server模式和client模式，所有slave机器都需要安装glances的server，master开启client模式就可以收集所有slave的数据（类似prometheus这样的pull模式）。所以也可以理解为局域网下的prom（glances不适合在公网做monitor），感觉意思不大。
