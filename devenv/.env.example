# ===========================================
# 开发环境配置文件示例
# 复制此文件为 .env 并根据需要修改配置
# ===========================================

# 基础配置
TZ=Asia/Shanghai
NETWORKS_DRIVER=bridge

# 代码和数据路径
CODE_PATH_HOST=../
DATA_PATH_HOST=./data

# ===========================================
# 数据库配置
# ===========================================

# MySQL 配置
MYSQL_VERSION=5.7
MYSQL_PORT=3306
MYSQL_USERNAME=devuser
MYSQL_PASSWORD=devpass
MYSQL_ROOT_PASSWORD=rootpass
MYSQL_DATABASE=devdb

# Redis 配置
REDIS_VERSION=7-alpine
REDIS_PORT=6379

# ETCD 配置
ETCD_PORT=2379

# ClickHouse 配置
CLICKHOUSE_VERSION=23.8
CLICKHOUSE_HTTP_PORT=8123
CLICKHOUSE_TCP_PORT=9000
CLICKHOUSE_USER=gotomicro
CLICKHOUSE_PASSWORD=clickhouse

# ===========================================
# 管理工具端口配置
# ===========================================

# MySQL 管理工具 (phpMyAdmin)
MYSQL_MANAGE_PORT=8080
MYSQL_MANAGE_CONNECT_HOST=mysql
MYSQL_MANAGE_CONNECT_PORT=3306
MYSQL_MANAGE_USERNAME=root
MYSQL_MANAGE_ROOT_PASSWORD=rootpass

# Redis 管理工具 (Redis Commander)
REDIS_MANAGE_PORT=8081
REDIS_MANAGE_CONNECT_HOST=redis
REDIS_MANAGE_CONNECT_PORT=6379
REDIS_MANAGE_USERNAME=admin
REDIS_MANAGE_PASSWORD=admin

# ETCD 管理工具 (ETCD Keeper)
ETCD_MANAGE_PORT=8082

# ===========================================
# 监控服务配置 - PAG Stack
# ===========================================

# Prometheus 配置
PROMETHEUS_VERSION=v2.17.1
PROMETHEUS_PORT=9090
PROMETHEUS_RETENTION=200h

# AlertManager 配置
ALERTMANAGER_VERSION=v0.20.0
ALERTMANAGER_PORT=9093

# Grafana 配置
GRAFANA_VERSION=6.7.2
GRAFANA_PORT=3000
GRAFANA_USER=admin
GRAFANA_PASSWORD=admin

# Node Exporter 配置
NODE_EXPORTER_VERSION=v0.18.1
NODE_EXPORTER_PORT=9100

# cAdvisor 配置
CADVISOR_VERSION=v0.34.0
CADVISOR_PORT=8080

# Pushgateway 配置
PUSHGATEWAY_VERSION=v1.2.0
PUSHGATEWAY_PORT=9091

# Caddy 反向代理配置
CADDY_VERSION=latest
CADDY_ADMIN_USER=admin
CADDY_ADMIN_PASSWORD=admin
CADDY_GRAFANA_PORT=3001
CADDY_PROMETHEUS_PORT=9099
CADDY_ALERTMANAGER_PORT=9094
CADDY_PUSHGATEWAY_PORT=9092

# 告警配置（可选）
# SLACK_CHANNEL=#alerts
# SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# ===========================================
# 链路追踪配置
# ===========================================

# Jaeger 配置
JAEGER_VERSION=1.28
JAEGER_PORT=16686
JAEGER_OTLP_PORT=4317
JAEGER_OTLP_HTTP_PORT=4318

# ===========================================
# 微服务组件配置
# ===========================================

# DTM 分布式事务管理
DTM_HTTP_PORT=36789
DTM_GRPC_PORT=36790

# ===========================================
# 开发环境端口配置
# ===========================================

# Golang 开发环境端口
GOLANG_PORT_8000=8000
GOLANG_PORT_8001=8001
GOLANG_PORT_8002=8002
GOLANG_PORT_8003=8003
GOLANG_PORT_9000=9000
GOLANG_PORT_9001=9001
GOLANG_PORT_9002=9002
GOLANG_PORT_9003=9003

# ===========================================
# Docker Compose Profiles 说明
# ===========================================
# 可用的 profiles:
# - dev: 开发环境 (golang)
# - db: 数据库服务 (mysql, redis, etcd)
# - cache: 缓存服务 (redis)
# - monitor: 监控服务 (prometheus, grafana, alertmanager)
# - pag: PAG监控栈 (prometheus, alertmanager, grafana, nodeexporter, cadvisor, pushgateway, caddy)
# - trace: 链路追踪 (jaeger)
# - ms: 微服务组件 (etcd, dtm)
# - analytics: 分析数据库 (clickhouse)
# - full: 所有服务

# 使用示例:
# podman-compose --profile db up -d                    # 只启动数据库服务
# podman-compose --profile dev --profile db up -d      # 启动开发环境和数据库
# podman-compose --profile pag up -d                   # 启动PAG监控栈
# podman-compose --profile full up -d                  # 启动所有服务
